package com.x5.logistics.service.complexvalues

import com.x5.logistics.data.dictionary.BiComplexValueEntity
import com.x5.logistics.data.dictionary.BiComplexValuesTable
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesCreateRequest
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesUpdateRequest
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.not
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Service
import java.time.Instant

@Service
class ComplexValuesService {

    suspend fun getAllAvailableValues(user: String): List<BiComplexValueEntity> = newSuspendedTransaction {
        BiComplexValueEntity.find {
            (BiComplexValuesTable.deleted eq false) and
                    ((BiComplexValuesTable.createdBy eq user) or
                            (BiComplexValuesTable.type eq "public"))
        }.toList()
            .sortedWith(
                compareBy<BiComplexValueEntity> { it.type != "public" }
                    .thenBy { it.name.lowercase() }
            )
    }

    suspend fun getValuesByIds(ids: List<Int>): List<BiComplexValueEntity> = newSuspendedTransaction {
        BiComplexValueEntity.forIds(ids).toList()
            .sortedBy { it.name.lowercase() }
    }

    suspend fun createComplexValue(request: ComplexValuesCreateRequest, username: String): BiComplexValueEntity =
        newSuspendedTransaction {
            // Проверка на уникальность имени (без учета регистра)
            val existingWithSameName = BiComplexValueEntity.find {
                (BiComplexValuesTable.deleted eq false) and
                        (BiComplexValuesTable.createdBy eq username) and
                        (BiComplexValuesTable.name.lowerCase() eq request.name.lowercase())
            }.firstOrNull()

            if (existingWithSameName != null) {
                throw WrongRequestDataException("Показатель с таким именем уже существует")
            }

            val now = Instant.now()

            BiComplexValueEntity.new {
                name = request.name
                type = "private"
                settings = request.settings
                createdBy = username
                createdAt = now
                updatedAt = now
                deleted = false
            }
        }

}
